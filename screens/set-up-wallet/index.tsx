import Safe, { PredictedSafeProps } from '@safe-global/protocol-kit';
import * as Clipboard from 'expo-clipboard';
import { useNavigation } from 'expo-router';
import { useState } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { privateKeyToAddress } from 'viem/accounts';
import { sepolia } from 'viem/chains';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { useSafeContext } from '@/context/safeContext';
import { DEFAULT_VALIDITY_BLOCKS, RecoveryPurpose, TRecoveryKey, useAddRecoveryKeys } from '@/hooks/useAddRecoveryKeys';
import { TWallet, useCreateWallet } from '@/hooks/useCreateWallet';
import { useDeploySafe } from '@/hooks/useDeploySafe';
import { useGetLocation } from '@/hooks/useGetLocation';
import { useGetRecoveryKey } from '@/hooks/useGetRecoveryKey';
import { useGetRecoveryKeysNewWallet } from '@/hooks/useGetRecoveryKeysNewWallet';
import { useRegisterSafe } from '@/hooks/useRegisterSafe';
import { useSetupWallet } from '@/hooks/useSetupWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { useWalletStore } from '@/store/wallet';

type Props = {};

type TSigner = TWallet & { address: string };

export const SetupWallet = (props: Props) => {
  const { styles } = useStyles();
  const _navigation = useNavigation();
  const [signers, setSigners] = useState<TSigner[]>([]);
  const [threshold, setThreshold] = useState<string>('');

  const { data: recoveryKey } = useGetRecoveryKey();
  const { data: recoveryKeys = [] } = useGetRecoveryKeysNewWallet();
  const { mutateAsync: createWallet, isPending: isCreatingWallet } = useCreateWallet();
  const { mutateAsync: setupWallet, isPending: isSettingUpWallet } = useSetupWallet();
  const { mutateAsync: getLocation, isPending: isFetchingLocation } = useGetLocation();
  const { onSetSigners, onSetThreshold, isLoading } = useSafeContext();
  const { mutateAsync: deploySafe, isPending: isDeployingSafe } = useDeploySafe();
  const { setSafeWallet } = useWalletStore();
  const { mutateAsync: registerSafe } = useRegisterSafe();
  const { mutateAsync: addRecoveryKeys } = useAddRecoveryKeys();

  async function handleGenMultisig() {
    // startTransition(async () => {
    //   try {
    //     let location = await getLocation();
    //     console.log(location);
    //     let retry = 0;

    //     while (!location || !location?.coords?.accuracy || location.coords.accuracy > 30) {
    //       retry++;
    //       if (retry >= 10) break;
    //       await new Promise((resolve) => setTimeout(resolve, 3000));

    //       location = await getLocation();
    //     }

    //     if (!location || !location?.coords?.accuracy || location.coords.accuracy > 30) return;

    //     const wallets = await Promise.all(recoveryKeys.map((recoveryKey) => createWallet({ recoveryKey, location })));
    //     const signersAddress = await Promise.all(
    //       wallets.map((wallet) => privateKeyToAddress(`0x${wallet?.privateKey}` as const))
    //     );

    //     setSigners(signersAddress);

    //     // await setupWallet({ privateKey, seedPhrase, recoveryKey });

    //     // navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: '(app)(tabs)' }] }));

    //     // console.log(wallets);
    //   } catch (error) {
    //     console.error(error);
    //   }
    // });

    try {
      let location = await getLocation();
      console.log(location);
      let retry = 0;

      while (!location || !location?.coords?.accuracy || location.coords.accuracy > 30) {
        retry++;
        if (retry >= 10) break;
        await new Promise((resolve) => setTimeout(resolve, 3000));

        location = await getLocation();
      }

      if (!location || !location?.coords?.accuracy || location.coords.accuracy > 30) return;

      const wallets = await Promise.all(
        recoveryKeys.map(async (recoveryKey) => {
          const w = await createWallet({ recoveryKey, location });
          if (!w) throw new Error('Create wallet failed');

          const wAddress = privateKeyToAddress(`0x${w?.privateKey}` as const);
          return { ...w, address: wAddress };
        })
      );
      setSigners(wallets);

      // await setupWallet({ privateKey, seedPhrase, recoveryKey });

      // navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: '(app)(tabs)' }] }));

      // console.log(wallets);
    } catch (error) {
      console.error(error);
    }
  }

  const handleCopy = async (recoveryKey: string) => {
    if (!recoveryKey) return;

    await Clipboard.setStringAsync(recoveryKey);
  };

  const handleChangeThreshold = (text: string) => {
    if (text.length === 0) {
      setThreshold('');
    }

    if (text.match(/^\d+$/) && Number.parseInt(text) <= signers.length) {
      setThreshold(Number.parseInt(text).toString());
    }
  };

  const handleSetupWallet = async () => {
    try {
      const predictedSafe: PredictedSafeProps = {
        safeAccountConfig: {
          owners: signers.map((signer) => signer.address),
          threshold: Number.parseInt(threshold),
        },
      };

      const protocolKit = await Safe.init({
        provider: sepolia.rpcUrls.default.http[0],
        predictedSafe: predictedSafe,
        signer: process.env.EXPO_PUBLIC_WALLET_PK,
      });

      const safeAddress = await protocolKit.getAddress();
      console.log('🚀 ~ handleSetupWallet ~ safeAddress:', safeAddress);
      const isDeployed = await protocolKit.isSafeDeployed();

      // Deploy safe
      if (isDeployed) {
        await deploySafe(protocolKit);
      }

      // Register safe
      const registerSafeReceipt = await registerSafe({
        minKeysRequired: BigInt(Number.parseInt(threshold)),
        safeAddress,
        protocolKit,
      });
      if (registerSafeReceipt.status !== 'success') {
        throw new Error('Register safe failed');
      }

      const recoveryKeysMap = await Promise.all(
        recoveryKeys.map(async (recoveryKey, index) => {
          const key: TRecoveryKey = {
            keyId: recoveryKey,
            safe: safeAddress,
            expectedAddress: signers[index],
            validUntilBlock: BigInt(DEFAULT_VALIDITY_BLOCKS),
            isUsed: false,
            purpose: RecoveryPurpose.BOTH,
            hint: '',
          };

          return key;
        })
      );

      // Add recovery keys
      const addRecoveryKeysReceipt = await addRecoveryKeys({
        keys: recoveryKeysMap,
        safeAddress,
        protocolKit,
      });

      if (addRecoveryKeysReceipt.status !== 'success') {
        throw new Error('Add recovery keys failed');
      }

      // Store safe address local
      setSafeWallet(safeAddress);

      // Store first signer local
      const firstSigner = signers[0];
      const firstRecoveryKey = recoveryKeys[0];
      await setupWallet({
        privateKey: firstSigner.privateKey,
        seedPhrase: firstSigner.seedPhrase,
        recoveryKey: firstRecoveryKey,
      });

      // navigation.dispatch(
      //   CommonActions.reset({
      //     index: 0,
      //     routes: [{ name: '(tabs)' }],
      //   })
      // );
    } catch (error) {
      console.error(error);
    }
  };

  const isPending = isCreatingWallet || isSettingUpWallet || isFetchingLocation;

  return (
    <KeyboardAvoidingView behavior='padding' style={styles.fullFlex}>
      <SafeAreaView edges={['bottom']} style={styles.container}>
        <FlatList
          data={signers}
          renderItem={({ item, index }) => (
            <View style={styles.containerKey} key={item.address} pointerEvents='auto'>
              <View style={styles.boxKey} pointerEvents='auto'>
                <ThemedText type='tinyLight' style={styles.keyTitle}>
                  {`Signer Address ${index + 1}`}
                </ThemedText>

                <ThemedText>{item.address}</ThemedText>
              </View>

              <CustomButton type='secondary' onPress={() => handleCopy(item.address)}>
                <Icons.Copy size={20} color='#fff' />
              </CustomButton>
            </View>
          )}
          keyExtractor={(item) => item.address}
          contentContainerStyle={{ gap: 16 }}
          showsVerticalScrollIndicator={false}
        />

        <Spacer height={16} />

        <TextInput
          label='Threshold'
          editable={signers.length > 0}
          keyboardType='number-pad'
          value={threshold}
          onChangeText={handleChangeThreshold}
        />

        <Spacer height={16} />

        <CustomButton
          type='primary'
          onPress={signers.length === 0 ? handleGenMultisig : handleSetupWallet}
          disabled={isLoading || isPending || (Number(signers.length) > 0 && Number(threshold || 0) <= 0)}
          isLoading={isDeployingSafe || isPending}
        >
          {signers.length === 0 ? 'Generate multisig' : 'Setup wallet'}
        </CustomButton>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    fullFlex: {
      flex: 1,
    },
  });

  return { styles };
};
