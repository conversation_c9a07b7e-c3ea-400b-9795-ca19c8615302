import Safe from '@safe-global/protocol-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Account, Address, Chain, Transport, WalletClient } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';

export const DEFAULT_VALIDITY_BLOCKS = 6 * 30 * 24 * 60 * 4; // ~6 months

export enum RecoveryPurpose {
  ADD_SIGNER = 0, // Can only add new signer
  ADD_RECOVERY_KEYS = 1, // Can only add new recovery keys
  BOTH = 2, // Can do both operations
}

export type TRecoveryKey = {
  keyId: string;
  safe: Address;
  expectedAddress: Address;
  validUntilBlock: bigint;
  isUsed: boolean;
  purpose: number;
  hint: string;
};

type Payload = {
  protocolKit: Safe;
  safeAddress: Address;
  keys: TRecoveryKey[];
};

export const useAddRecoveryKeys = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ keys, safeAddress, protocolKit }: Payload) => {
      const signer = (await protocolKit.getSafeProvider().getExternalSigner()) as WalletClient<
        Transport,
        Chain,
        Account
      >;
      const client = protocolKit.getSafeProvider().getExternalProvider();

      if (!signer) throw new Error('SafeProvider must be initialized with a signer to use this function');

      const hash = await signer.writeContract({
        address: env.GEOSAFE_CONTRACT_ADDRESS,
        abi: GeoSafeAbi,
        functionName: 'addRecoveryKeys',
        args: [safeAddress, keys],
        account: signer.account,
      });

      const receipt = await waitForTransactionReceipt(client, { hash, confirmations: 1 });

      return receipt;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
