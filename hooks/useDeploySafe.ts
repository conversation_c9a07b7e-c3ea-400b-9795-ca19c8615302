import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { waitForTransactionReceipt } from 'viem/actions';

export const RECOVERY_KEY_STORE_KEY = 'geoSafeRecoveryKey';

export const useDeploySafe = () => {
  return useMutation({
    mutationFn: async (protocolKit: Safe) => {
      // const safeDeploymentTransaction = await protocolKit.createSafeDeploymentTransaction();

      // const signer = (await protocolKit.getSafeProvider().getExternalSigner()) as WalletClient<
      //   Transport,
      //   Chain,
      //   Account
      // >;
      const client = protocolKit.getSafeProvider().getExternalProvider();

      // if (!signer) throw new Error('SafeProvider must be initialized with a signer to use this function');

      // const hash = await signer.sendTransaction({
      //   to: safeDeploymentTransaction.to as `0x${string}`,
      //   data: safeDeploymentTransaction.data as Hex,
      //   value: BigInt(safeDeploymentTransaction.value),
      //   account: signer.account,
      // });

      const hash = '0x2a7cb0d0d37eb993956dfa768ce2ce1e406ab9b022a7b0e0c410090037ca1b19';

      console.log('🚀 ~ deploySafe ~ hash:', hash);

      const receipt = await waitForTransactionReceipt(client, { hash, confirmations: 1 });

      return receipt;
    },
  });
};
