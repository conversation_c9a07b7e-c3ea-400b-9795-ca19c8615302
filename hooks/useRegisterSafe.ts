import Safe from '@safe-global/protocol-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Account, Address, Chain, Transport, WalletClient } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';

type Payload = {
  protocolKit: Safe;
  safeAddress: Address;
  minKeysRequired: bigint;
};

export const useRegisterSafe = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ minKeysRequired, safeAddress, protocolKit }: Payload) => {
      const signer = (await protocolKit.getSafeProvider().getExternalSigner()) as WalletClient<
        Transport,
        Chain,
        Account
      >;
      const client = protocolKit.getSafeProvider().getExternalProvider();

      if (!signer) throw new Error('SafeProvider must be initialized with a signer to use this function');

      const hash = await signer.writeContract({
        address: env.GEOSAFE_CONTRACT_ADDRESS,
        abi: GeoSafeAbi,
        functionName: 'registerSafe',
        args: [safeAddress, minKeysRequired],
        account: signer.account,
      });

      const receipt = await waitForTransactionReceipt(client, { hash, confirmations: 1 });

      return receipt;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({ queryKey: queryKeys.setupPin });
    },
  });
};
