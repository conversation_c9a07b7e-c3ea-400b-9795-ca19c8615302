export const GeoSafeAbi = [
  {
    inputs: [{ internalType: 'address', name: '_geoSafeFactory', type: 'address' }],
    stateMutability: 'nonpayable',
    type: 'constructor',
  },
  { inputs: [], name: 'ActionExecutionFailed', type: 'error' },
  { inputs: [], name: 'CannotUseLastKeys', type: 'error' },
  { inputs: [], name: 'DuplicateKeyId', type: 'error' },
  {
    inputs: [
      { internalType: 'uint256', name: 'current', type: 'uint256' },
      { internalType: 'uint256', name: 'required', type: 'uint256' },
    ],
    name: 'InsufficientRecoveryKeys',
    type: 'error',
  },
  { inputs: [], name: 'InvalidAddress', type: 'error' },
  { inputs: [], name: 'InvalidKeyId', type: 'error' },
  { inputs: [], name: 'InvalidSignature', type: 'error' },
  { inputs: [], name: 'ModuleNotEnabled', type: 'error' },
  { inputs: [], name: 'RecoveryAlreadyCancelled', type: 'error' },
  { inputs: [], name: 'RecoveryAlreadyExecuted', type: 'error' },
  { inputs: [], name: 'RecoveryDelayNotPassed', type: 'error' },
  { inputs: [], name: 'RecoveryKeyAlreadyUsed', type: 'error' },
  { inputs: [], name: 'RecoveryKeyExpired', type: 'error' },
  { inputs: [], name: 'RecoveryKeyNotFound', type: 'error' },
  { inputs: [], name: 'RecoveryNotFound', type: 'error' },
  { inputs: [], name: 'RecoveryPaused', type: 'error' },
  { inputs: [], name: 'SafeAlreadyRegistered', type: 'error' },
  { inputs: [], name: 'SafeNotRegistered', type: 'error' },
  { inputs: [], name: 'TooManyKeys', type: 'error' },
  { inputs: [], name: 'Unauthorized', type: 'error' },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: true, internalType: 'bytes32', name: 'recoveryId', type: 'bytes32' },
    ],
    name: 'RecoveryCancelled',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: true, internalType: 'bytes32', name: 'recoveryId', type: 'bytes32' },
      { indexed: false, internalType: 'address', name: 'newSigner', type: 'address' },
    ],
    name: 'RecoveryExecuted',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: true, internalType: 'bytes32', name: 'recoveryId', type: 'bytes32' },
      { indexed: false, internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
    ],
    name: 'RecoveryInitiated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: true, internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
    ],
    name: 'RecoveryKeyUsed',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: false, internalType: 'bytes32[]', name: 'keyIds', type: 'bytes32[]' },
      { indexed: false, internalType: 'uint256', name: 'totalKeys', type: 'uint256' },
    ],
    name: 'RecoveryKeysAdded',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: false, internalType: 'uint256', name: 'minKeys', type: 'uint256' },
      { indexed: false, internalType: 'bool', name: 'paused', type: 'bool' },
    ],
    name: 'SafeConfigUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'safe', type: 'address' },
      { indexed: false, internalType: 'uint256', name: 'minKeys', type: 'uint256' },
    ],
    name: 'SafeRegistered',
    type: 'event',
  },
  {
    inputs: [],
    name: 'CALL',
    outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'DEFAULT_MIN_KEYS',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'DEFAULT_RECOVERY_DELAY',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'MAX_RECOVERY_KEYS',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'MIN_RECOVERY_KEYS',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'safe', type: 'address' },
      {
        components: [
          { internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
          { internalType: 'address', name: 'safe', type: 'address' },
          { internalType: 'address', name: 'expectedAddress', type: 'address' },
          { internalType: 'uint96', name: 'validUntilBlock', type: 'uint96' },
          { internalType: 'bool', name: 'isUsed', type: 'bool' },
          { internalType: 'enum GeoSafeRecovery.RecoveryPurpose', name: 'purpose', type: 'uint8' },
          { internalType: 'string', name: 'hint', type: 'string' },
        ],
        internalType: 'struct GeoSafeRecovery.RecoveryKey[]',
        name: 'keys',
        type: 'tuple[]',
      },
    ],
    name: 'addRecoveryKeys',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'safe', type: 'address' },
      { internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
    ],
    name: 'canUseRecoveryKey',
    outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'bytes32', name: 'recoveryId', type: 'bytes32' }],
    name: 'cancelRecovery',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'bytes32', name: 'recoveryId', type: 'bytes32' }],
    name: 'executeRecovery',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'geoSafeFactory',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'address', name: 'safe', type: 'address' }],
    name: 'getActiveKeyCount',
    outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'address', name: 'safe', type: 'address' }],
    name: 'getAvailableRecoveryKeys',
    outputs: [{ internalType: 'bytes32[]', name: '', type: 'bytes32[]' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'bytes32', name: 'keyId', type: 'bytes32' }],
    name: 'getRecoveryKeyDetails',
    outputs: [
      {
        components: [
          { internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
          { internalType: 'address', name: 'safe', type: 'address' },
          { internalType: 'address', name: 'expectedAddress', type: 'address' },
          { internalType: 'uint96', name: 'validUntilBlock', type: 'uint96' },
          { internalType: 'bool', name: 'isUsed', type: 'bool' },
          { internalType: 'enum GeoSafeRecovery.RecoveryPurpose', name: 'purpose', type: 'uint8' },
          { internalType: 'string', name: 'hint', type: 'string' },
        ],
        internalType: 'struct GeoSafeRecovery.RecoveryKey',
        name: '',
        type: 'tuple',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'address', name: 'safe', type: 'address' }],
    name: 'getRecoveryKeys',
    outputs: [{ internalType: 'bytes32[]', name: '', type: 'bytes32[]' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'address', name: 'safe', type: 'address' }],
    name: 'getSafeConfig',
    outputs: [
      {
        components: [
          { internalType: 'bool', name: 'isRegistered', type: 'bool' },
          { internalType: 'bool', name: 'pauseRecovery', type: 'bool' },
          { internalType: 'uint96', name: 'customRecoveryDelay', type: 'uint96' },
          { internalType: 'uint96', name: 'minKeysRequired', type: 'uint96' },
          { internalType: 'uint32', name: 'totalKeys', type: 'uint32' },
        ],
        internalType: 'struct GeoSafeRecovery.SafeConfig',
        name: '',
        type: 'tuple',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'safe', type: 'address' },
      { internalType: 'bytes32', name: 'recoveryKeyId', type: 'bytes32' },
      { internalType: 'bytes', name: 'signature', type: 'bytes' },
      { internalType: 'address', name: 'newSigner', type: 'address' },
      {
        components: [
          { internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
          { internalType: 'address', name: 'safe', type: 'address' },
          { internalType: 'address', name: 'expectedAddress', type: 'address' },
          { internalType: 'uint96', name: 'validUntilBlock', type: 'uint96' },
          { internalType: 'bool', name: 'isUsed', type: 'bool' },
          { internalType: 'enum GeoSafeRecovery.RecoveryPurpose', name: 'purpose', type: 'uint8' },
          { internalType: 'string', name: 'hint', type: 'string' },
        ],
        internalType: 'struct GeoSafeRecovery.RecoveryKey[]',
        name: 'newKeys',
        type: 'tuple[]',
      },
    ],
    name: 'initiateRecovery',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
    name: 'pendingRecoveries',
    outputs: [
      { internalType: 'address', name: 'safe', type: 'address' },
      { internalType: 'bytes32', name: 'recoveryKeyId', type: 'bytes32' },
      { internalType: 'address', name: 'initiator', type: 'address' },
      { internalType: 'address', name: 'newSigner', type: 'address' },
      { internalType: 'uint256', name: 'executionTime', type: 'uint256' },
      { internalType: 'bool', name: 'executed', type: 'bool' },
      { internalType: 'bool', name: 'cancelled', type: 'bool' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
    name: 'recoveryKeys',
    outputs: [
      { internalType: 'bytes32', name: 'keyId', type: 'bytes32' },
      { internalType: 'address', name: 'safe', type: 'address' },
      { internalType: 'address', name: 'expectedAddress', type: 'address' },
      { internalType: 'uint96', name: 'validUntilBlock', type: 'uint96' },
      { internalType: 'bool', name: 'isUsed', type: 'bool' },
      { internalType: 'enum GeoSafeRecovery.RecoveryPurpose', name: 'purpose', type: 'uint8' },
      { internalType: 'string', name: 'hint', type: 'string' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'safe', type: 'address' },
      { internalType: 'uint256', name: 'minKeysRequired', type: 'uint256' },
    ],
    name: 'registerSafe',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [{ internalType: 'address', name: '', type: 'address' }],
    name: 'safeConfigs',
    outputs: [
      { internalType: 'bool', name: 'isRegistered', type: 'bool' },
      { internalType: 'bool', name: 'pauseRecovery', type: 'bool' },
      { internalType: 'uint96', name: 'customRecoveryDelay', type: 'uint96' },
      { internalType: 'uint96', name: 'minKeysRequired', type: 'uint96' },
      { internalType: 'uint32', name: 'totalKeys', type: 'uint32' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '', type: 'address' },
      { internalType: 'uint256', name: '', type: 'uint256' },
    ],
    name: 'safeRecoveryKeys',
    outputs: [{ internalType: 'bytes32', name: '', type: 'bytes32' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: 'safe', type: 'address' },
      { internalType: 'uint256', name: 'newMinKeys', type: 'uint256' },
      { internalType: 'bool', name: 'pauseRecovery', type: 'bool' },
    ],
    name: 'updateSafeConfig',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
] as const;
