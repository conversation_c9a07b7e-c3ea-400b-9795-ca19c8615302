import { Redirect, Stack, useSegments } from 'expo-router';

import { Colors } from '@/constants/Colors';
import { useGetWallet } from '@/hooks/useGetWallet';
import { SafeProvider } from '@/utils/safeProvider';

export default function AppLayout() {
  // const isFirstTime = useCommonStore.use.isFirstTime();

  const { data: wallet, isPending } = useGetWallet();
  const { privateKey, seedPhrase } = wallet || {};

  const segments = useSegments();

  console.log(segments);

  // if (isFirstTime) {
  //   return (
  //     <Redirect
  //       href={{
  //         pathname: '/onboarding',
  //         params: {
  //           name: 'Onboarding',
  //         },
  //       }}
  //     />
  //   );
  // }

  if (isPending) return null;

  if (!privateKey || !seedPhrase) {
    return <Redirect href='/onboarding' />;
  }

  return (
    <SafeProvider>
      <Stack
        initialRouteName='(tabs)'
        screenOptions={{
          headerStyle: { backgroundColor: Colors.dark.background },
          contentStyle: { backgroundColor: Colors.dark.background },
        }}
      >
        <Stack.Screen name='(tabs)' options={{ title: '' }} />

        {/* <Stack.Screen name='(tabs)' options={{ headerShown: false }} /> */}
      </Stack>
    </SafeProvider>
  );
}
